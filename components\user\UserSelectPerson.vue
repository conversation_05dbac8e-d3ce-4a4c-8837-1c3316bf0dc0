<script lang="ts" setup>
import {
  Modal,
  Checkbox as hCheckbox,
  Input as hInput,
  Button as hButton,
  Row as hRow,
  Col as hCol,
  Divider as hDivider,
  message,
} from 'ant-design-vue';
import { ref, reactive, onMounted, watch, defineEmits, defineProps, withDefaults, computed } from 'vue';
import { userApi } from '@haierbusiness-front/apis';
import { IUserListRequest, IUserInfo } from '@haierbusiness-front/common-libs';
import { debounce } from 'lodash';
import { EditOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';

interface Props {
  value?: IUserInfo[];
  params?: IUserListRequest;
  placeholder?: string;
  showWorkNumber?: boolean;
  showPhoneNumber?: boolean;
  width?: string | number;
  maxTagCount?: number;
  maxCount?: number;
  multiple?: boolean;
  radio?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  params: () => ({}),
  placeholder: '请选择',
  showWorkNumber: true,
  showPhoneNumber: false,
  width: '100%',
  maxTagCount: 3,
  maxCount: 999,
  multiple: false,
  radio: false,
  disabled: false,
});
console.log('准备数据:', props.value);

const emit = defineEmits(['change', 'deselect', 'update:value']);
const spinning = ref(false);
// 弹窗可见性
const modalVisible = ref(false);
// 已选择的人员列表
const selectedUsers = ref<IUserInfo[]>([]);
// 临时存储弹窗中选择的人员，只有点击确定后才会更新到selectedUsers
const tempSelectedUsers = ref<IUserInfo[]>([]);
// 搜索关键词
const searchKeyword = ref('');
// 搜索结果
const searchResults = ref<IUserInfo[]>([]);
// 加载状态
const loading = ref(false);
// 确定按钮加载状态
const confirmLoading = ref(false);
// 分页参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 20,
});
const ifInGroup = ref(false);
// 总数
const total = ref(0);

const checkedGroup = ref(false);
// 新增：用户组名称弹窗显示状态和输入内容
const groupModalVisible = ref(false);
const groupName = ref('');

// 计算显示的用户标签和隐藏的数量
const displayedUsers = computed(() => {
  if (selectedUsers.value.length <= props.maxTagCount) {
    return selectedUsers.value;
  }
  return selectedUsers.value.slice(0, props.maxTagCount);
});

const hiddenCount = computed(() => {
  return Math.max(0, selectedUsers.value.length - props.maxTagCount);
});

// 打开选择弹窗
const openModal = () => {
  if (props.disabled) return;
  modalVisible.value = true;
  // 重置分页参数
  pageParams.value = {
    pageNum: 1,
    pageSize: 20,
  };
  searchKeyword.value = '';
  searchResults.value = [];
  // 初始化临时选择列表为当前已选择的用户
  tempSelectedUsers.value = [...selectedUsers.value];
  fetchUserData();
};
const groupCurrent = reactive({});
const groupGet = async (group, ifChecked) => {
  const res = await userApi.queryGroupUser({
    groupId: group.id,
  });
  if (res.userInfoList?.length > 0) {
    res.userInfoList.forEach((item) => {
      if (ifChecked) {
        if (!tempSelectedUsers.value.map((user) => String(user.id)).includes(String(item.id)))
          tempSelectedUsers.value.push({ ...item });
      } else {
        tempSelectedUsers.value = tempSelectedUsers.value.filter((user) => user.id != item.id);
        searchResults.value.forEach((item) => {
          if (item.groupChecked) groupGet(item, item.groupChecked);
        });
      }
    });
  }
};
const getGroupUser = async (group) => {
  spinning.value = true;
  Object.assign(groupCurrent, group);
  ifInGroup.value = true;
  const res = await userApi.queryGroupUser({
    groupId: group.id,
  });
  if (res.userInfoList?.length > 0) {
    searchResults.value = res.userInfoList;
  }
  spinning.value = false;
};
const deleteGroup = async (group) => {
  let res;
  if (ifInGroup.value) {
    res = await userApi.deleteUserLink({
      groupId: groupCurrent.id,
      userId: group.id,
    });
  } else {
    res = await userApi.deleteGroup({
      groupId: group.id,
    });
  }

  if (res.success) {
    message.success(`删除${ifInGroup.value ? '人员' : '用户组'}成功`);
    searchResults.value = searchResults.value.filter((item) => item.id !== group.id);
  }
};
const backupList = () => {
  ifInGroup.value = false;
  pageParams.value = {
    pageNum: 1,
    pageSize: 20,
  };
  fetchUserData();
};
// 获取用户数据
const fetchUserData = async (isScroll: boolean = false) => {
  spinning.value = true;
  loading.value = true;
  try {
    const params: IUserListRequest = {
      ...props.params,
    };

    // 确保pageNum和pageSize正确传递
    params.pageNum = pageParams.value.pageNum;
    params.pageSize = pageParams.value.pageSize;

    if (searchKeyword.value) {
      params.keyWord = searchKeyword.value;
    }

    let res = await userApi.list(params);

    if (res && res.records) {
      if (isScroll) {
        // 滚动加载，追加数据
        searchResults.value = [...searchResults.value, ...res.records];
      } else {
        const groupRes = await userApi.groupList();
        res.records = groupRes.concat(res.records);
        // 首次加载或搜索，替换数据
        searchResults.value = res.records;
      }
      total.value = res.total ?? 0;
    }
  } catch (error) {
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
    spinning.value = false;
  }
};

// 搜索用户 - 添加debounce防抖
const handleSearch = debounce(async () => {
  // 重置分页参数
  pageParams.value = {
    pageNum: 1,
    pageSize: pageParams.value.pageSize,
  };
  searchResults.value = [];
  await fetchUserData();
}, 500);

// 监听搜索框输入
const onSearchInput = () => {
  handleSearch();
};

// 滚动加载
const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  const { clientHeight, scrollHeight, scrollTop } = target;

  // 判断滚动条是否接近底部，且当前没有正在加载数据
  if (scrollHeight - scrollTop - clientHeight < 50 && !loading.value && !ifInGroup.value) {
    if (searchResults.value.length < total.value) {
      pageParams.value.pageNum += 1;
      fetchUserData(true);
    }
  }
};

// 选择/取消选择用户 - 只修改临时选择列表
const toggleUserSelection = (user: IUserInfo) => {
  if (props.radio) tempSelectedUsers.value = [];
  const index = tempSelectedUsers.value.findIndex((u) => u.username === user.username);
  if (index > -1) {
    tempSelectedUsers.value.splice(index, 1);
  } else {
    // 保存用户的完整原始数据
    tempSelectedUsers.value.push({ ...user });
  }
};
const groupCheck = (user) => {
  if (props.radio) {
    getGroupUser(user);
    return;
  }
  if (user.groupChecked === undefined) user.groupChecked = true;
  else {
    user.groupChecked = !user.groupChecked;
  }
  groupGet(user, user.groupChecked);
};
// 用户是否已选中 - 使用临时选择列表判断
const isUserSelected = (user: IUserInfo) => {
  return tempSelectedUsers.value.some((u) => u.username === user.username);
};

// 全选 - 只修改临时选择列表
const selectAll = () => {
  if (props.radio) return;
  searchResults.value.forEach((element) => {
    if (element.groupName != undefined) element.groupChecked = true;
  });
  searchResults.value.forEach((user) => {
    if (!isUserSelected(user) && user.groupName === undefined) {
      // 保存用户的完整原始数据
      tempSelectedUsers.value.push({ ...user });
    }
  });
};

// 全不选 - 只修改临时选择列表
const deselectAll = () => {
  searchResults.value.forEach((element) => {
    if (element.groupName != undefined) element.groupChecked = false;
  });
  tempSelectedUsers.value = [];
};

// 确认选择
const confirmSelection = async () => {
  if (checkedGroup.value) {
    // 弹出填写用户组名称弹窗
    groupModalVisible.value = true;
    return;
  }
  // 更新实际选择的用户列表
  selectedUsers.value = [...tempSelectedUsers.value];
  confirmLoading.value = true;
  setTimeout(() => {
    emit('change', [...selectedUsers.value]);
    emit('update:value', [...selectedUsers.value]);
    modalVisible.value = false;
    confirmLoading.value = false;
  }, 300);
};

// 用户组名称弹窗确认
const handleGroupModalOk = async () => {
  if (!groupName.value.trim()) {
    message.error('请输入用户组名称');
    return;
  }
  // 这里假设 saveGroup 需要传递 { name, users }
  try {
    confirmLoading.value = true;
    const res = await userApi.saveGroup({
      groupName: groupName.value,
      userIdList: tempSelectedUsers.value.map((item) => item.id),
    });
    if (res?.success) message.success('创建用户组成功！');
    // 关闭所有弹窗，清空输入
    groupModalVisible.value = false;
    modalVisible.value = false;
    groupName.value = '';
    // 更新实际选择的用户列表
    selectedUsers.value = [...tempSelectedUsers.value];
    emit('change', [...selectedUsers.value]);
    emit('update:value', [...selectedUsers.value]);
  } catch (e) {
    message.error('创建用户组失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 用户组名称弹窗取消
const handleGroupModalCancel = () => {
  groupModalVisible.value = false;
  groupName.value = '';
};

// 关闭弹窗 - 放弃临时选择
const closeModal = () => {
  modalVisible.value = false;
  // 放弃临时选择的更改
  tempSelectedUsers.value = [...selectedUsers.value];
};

// 初始化已选用户
onMounted(() => {
  if (props.value && props.value.length > 0) {
    // 确保使用完整的用户数据
    selectedUsers.value = props.value;
    tempSelectedUsers.value = [...selectedUsers.value];
  }
});
// 设置初始数据
const setFirstData = (users: IUserInfo[]) => {
  if (users && Array.isArray(users)) {
    // 确保使用完整的用户数据
    selectedUsers.value = users.map((user) => ({ ...user }));
    tempSelectedUsers.value = [...selectedUsers.value];
  }
};

// 对外暴露方法
defineExpose({
  setFirstData,
  openModal,
});
</script>

<template>
  <div class="user-select-person" :style="{ width: width }">
    <!-- 选择区域 -->
    <div class="select-area" :class="{ disabled: disabled }" @click="openModal">
      <div class="selected-users" v-if="selectedUsers.length > 0">
        <div v-for="(user, index) in displayedUsers" :key="user.username" class="selected-user-tag">
          <span>{{ user.nickName }} ({{ user.username }})</span>
        </div>
        <div v-if="hiddenCount > 0" class="selected-user-tag more-tag">
          <span>+{{ hiddenCount }}</span>
        </div>
      </div>
      <div v-else class="placeholder">{{ placeholder }}</div>
    </div>

    <!-- 选择人员弹窗 -->
    <Modal title="选择人员" :visible="modalVisible" @cancel="modalVisible = false" width="900px" :footer="null">
      <!-- 搜索框 -->
      <div class="modal-header">
        <div class="search-box">
          <h-input
            v-model:value="searchKeyword"
            placeholder="请输入"
            style="width: calc(100% - 100px); margin-right: 8px"
            @input="onSearchInput"
            :maxlength="200"
          />
          <h-button type="primary" @click="handleSearch" :loading="loading">搜索</h-button>
        </div>
      </div>

      <!-- 左右布局选择区域 -->
      <h-row :gutter="16" class="selection-row">
        <!-- 左侧：可选择的用户列表 -->
        <h-col :span="12">
          <a-spin :spinning="spinning">
            <div class="left-panel">
              <div class="panel-header">
                <span class="panel-title">{{ ifInGroup ? '组内人员' : '可选人员' }}</span>
                <div class="operation-buttons">
                  <a v-if="ifInGroup" @click="backupList">返回</a>
                  <h-divider v-if="ifInGroup" type="vertical" />
                  <a @click="selectAll" v-if="!props.radio">全选</a>
                  <h-divider v-if="!props.radio" type="vertical" />
                  <a @click="deselectAll">全不选</a>
                </div>
              </div>
              <div class="user-list">
                <div class="list-header">
                  <h-checkbox
                    :indeterminate="tempSelectedUsers.length > 0 && tempSelectedUsers.length < searchResults.length"
                    :checked="tempSelectedUsers.length === searchResults.length && searchResults.length > 0"
                    @change="tempSelectedUsers.length === searchResults.length ? deselectAll() : selectAll()"
                  />
                  <span class="header-item">工号</span>
                  <span class="header-item">名称</span>
                  <span class="header-item">部门/操作</span>
                </div>
                <div class="list-content" @scroll="handleScroll">
                  <div v-if="loading && searchResults.length === 0" class="loading-state">加载中...</div>
                  <template v-else>
                    <div
                      v-for="user in searchResults"
                      :key="user.username"
                      @click="user.groupName === undefined ? toggleUserSelection(user) : groupCheck(user)"
                      class="user-item"
                    >
                      <h-checkbox
                        :checked="user.groupName === undefined ? isUserSelected(user) : user.groupChecked"
                        @click.stop
                        @click="user.groupName === undefined ? toggleUserSelection(user) : groupCheck(user)"
                      />
                      <span class="item-info">{{ user.username || '用户组' }}</span>
                      <span class="item-info">{{ user.nickName || user.groupName }}</span>
                      <span class="item-info" @click.stop v-if="user.groupName || ifInGroup">
                        <a-button type="link" v-if="user.groupName" @click="getGroupUser(user)"
                          ><EditOutlined
                        /></a-button>
                        <a-popconfirm
                          @click.stop
                          :title="ifInGroup ? '确认删除用户组该人员吗？' : '确认删除该用户组吗？'"
                          @confirm="deleteGroup(user)"
                        >
                          <a-button type="link" @click.stop><CloseCircleOutlined /></a-button>
                        </a-popconfirm>
                      </span>
                      <span v-else class="item-info">{{ user.departmentName }}</span>
                    </div>
                    <!-- 滚动加载提示 -->
                    <div v-if="loading && searchResults.length > 0" class="loading-more">
                      <span>加载更多...</span>
                    </div>
                    <!-- 没有更多数据提示 -->
                    <div v-else-if="searchResults.length > 0 && searchResults.length >= total" class="no-more">
                      <span>没有更多数据了</span>
                    </div>
                    <div v-if="searchResults.length === 0 && !loading" class="empty-state">暂无数据</div>
                  </template>
                </div>
              </div>
            </div>
          </a-spin>
        </h-col>

        <!-- 右侧：已选择的用户回显 -->
        <h-col :span="12">
          <div class="right-panel">
            <div class="panel-header">
              <span class="panel-title">已选人员 ({{ tempSelectedUsers.length }})</span>
              <a v-if="tempSelectedUsers.length > 0" @click="deselectAll" class="clear-all">清空</a>
            </div>
            <div class="selected-list">
              <div v-for="(user, index) in tempSelectedUsers" :key="user.username" class="selected-item">
                <div class="user-info">
                  <div class="user-name">{{ user.nickName }}({{ user.username }})</div>
                </div>
                <span class="remove-btn" @click="tempSelectedUsers.splice(index, 1)" title="移除">×</span>
              </div>
              <div v-if="tempSelectedUsers.length === 0" class="empty-selected">请从左侧选择人员</div>
            </div>
          </div>
        </h-col>
      </h-row>

      <!-- 自定义底部按钮 -->
      <div class="modal-footer">
        <a-checkbox v-if="!props.radio" v-model:checked="checkedGroup">同时创建用户组</a-checkbox>
        <h-button @click="closeModal" class="cancel-btn">取消</h-button>
        <h-button type="primary" @click="confirmSelection" :loading="confirmLoading" class="confirm-btn">确定</h-button>
      </div>
    </Modal>

    <!-- 新增：用户组名称弹窗 -->
    <Modal
      title="请输入用户组名称"
      :visible="groupModalVisible"
      @ok="handleGroupModalOk"
      @cancel="handleGroupModalCancel"
      :confirmLoading="confirmLoading"
      width="400px"
    >
      <h-input v-model:value="groupName" placeholder="请输入用户组名称" maxlength="50" />
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.user-select-person {
  width: 100%;
}

.select-area {
  display: flex;
  align-items: center;
  min-height: 32px;
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    border-color: #40a9ff;
  }

  &.disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;

    &:hover {
      border-color: #d9d9d9;
    }

    .placeholder {
      color: #bfbfbf;
    }

    .selected-user-tag {
      background-color: #f0f0f0;
      color: #999;
    }
  }
}

.select-label {
  margin-right: 8px;
}

.selected-users {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.selected-user-tag {
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  white-space: nowrap;

  &.more-tag {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.placeholder {
  color: #bfbfbf;
}

.modal-header {
  margin-bottom: 16px;
}

.selection-row {
  height: 420px;
  /* 固定高度 */
}

.left-panel,
.right-panel {
  height: 97%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 防止内容溢出 */
  box-sizing: border-box;
  /* 确保边框计算在内 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 4px 4px 0 0;
  flex-shrink: 0;
  /* 防止头部被压缩 */
  height: 48px;
  /* 固定头部高度 */
  box-sizing: border-box;
}

.panel-title {
  font-weight: 500;
  color: #262626;
}

.operation-buttons {
  display: flex;
  align-items: center;
  gap: 0;

  a {
    color: #1890ff;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      color: #40a9ff;
    }
  }
}

.clear-all {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    color: #40a9ff;
  }
}

.user-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 重要：允许flex子项收缩 */
  height: calc(100% - 48px);
  /* 减去头部高度 */
}

.list-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
  /* 防止头部被压缩 */
  height: 36px;
  /* 固定列表头部高度 */
  box-sizing: border-box;

  .header-item {
    flex: 1;
    margin-left: 8px;
  }
}

.list-content {
  overflow: auto;
  /* 启用垂直滚动 */
  height: 350px;
  position: relative;
  /* 为加载状态提供定位上下文 */
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 40px;
  /* 最小高度 */

  &:hover {
    background-color: #f5f5f5;
  }

  .item-info {
    flex: 1;
    margin-left: 8px;
    font-size: 13px;
    // display: flex;
    // justify-content: space-between;
  }
}

.empty-state,
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #999;
  font-size: 14px;
}

.selected-list {
  padding: 8px;
  overflow: auto;
  min-height: 0;
  height: 400px;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f6f6f6;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 40px;
  /* 最小高度 */

  &:hover {
    background-color: #e6f7ff;
  }
}

.user-info {
  flex: 1;

  .user-name {
    font-size: 13px;
    color: #262626;
    margin-bottom: 2px;
  }

  .user-code {
    font-size: 12px;
    color: #666;
  }

  .user-phone {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.remove-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #999;
  border-radius: 2px;
  transition: all 0.2s;
  flex-shrink: 0;

  &:hover {
    color: #fff;
    background-color: #ff4d4f;
  }
}

.empty-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #999;
  font-size: 14px;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;

  .cancel-btn {
    margin-right: 8px;
  }
}

.loading-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  color: #999;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.no-more {
  color: #ccc;
}
</style>
