<script setup lang="ts">
// 方案互动-方案合计
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { AttendantTypeConstant, MaterialTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  totalPrice: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['presentPriceEmit']);

const newSchemeList = ref<array>([]);

watch(
  () => props.schemeCacheInfo,
  async () => {
    newSchemeList.value = [];

    if (props.schemeCacheInfo) {
      // 住宿
      if (props.schemeCacheInfo.stays && props.schemeCacheInfo.stays.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let roomNum1 = 0;
        let roomNum2 = 0;
        let roomNum3 = 0;

        props.schemeCacheInfo.stays.forEach((e) => {
          if (!e.schemeUnitPrice) return;

          if (e.roomType === 1) {
            // 大床房（默认1人）
            roomNum1 += e.schemeRoomNum;
          }
          if (e.roomType === 2) {
            // 双床房
            roomNum2 += e.schemeRoomNum;
          }
          if (e.roomType === 3) {
            // 套房（默认1人）
            roomNum3 += e.schemeRoomNum;
          }

          demandTd3 += e.schemeRoomNum * e.schemeUnitPrice;
        });

        demandTd2 =
          (roomNum1 ? '大床房*' + roomNum1 + '间夜' : '') +
          ((roomNum1 && roomNum2) || (roomNum1 && roomNum3) ? '，' : '') +
          (roomNum2 ? '双床房*' + roomNum2 + '间夜' : '') +
          (roomNum2 && roomNum3 ? '，' : '') +
          (roomNum3 ? '套房*' + roomNum3 + '间夜' : '');

        newSchemeList.value.push({
          demandTd1: '住宿',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 会场
      if (props.schemeCacheInfo.places && props.schemeCacheInfo.places.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let isLed = false;
        let isTea = false;

        let ledNum = 0;
        let teaNum = 0;
        let ledPrice = 0;
        let teaPrice = 0;

        props.schemeCacheInfo.places.forEach((e) => {
          if (!e.schemeUnitPlacePrice) return;

          demandTd3 += e.schemeUnitPlacePrice || 0;

          if (e.hasLed) {
            if (!e.schemeUnitLedPrice) return;

            isLed = true;
            ledNum += e.schemeLedNum || 0;
            ledPrice += e.schemeUnitLedPrice * e.schemeLedNum || 0;
            // 单价*LED数量
            demandTd3 += e.schemeUnitLedPrice * e.schemeLedNum;
          }
          if (e.hasTea) {
            if (!e.teaEachTotalPrice) return;

            isTea = true;
            teaNum += e.schemePersonNum || 0;
            teaPrice += e.teaEachTotalPrice * e.schemePersonNum || 0;
            // 茶歇单价*会场人数
            demandTd3 += e.teaEachTotalPrice * e.schemePersonNum;
          }
        });

        demandTd2 =
          '会场*' +
          props.schemeCacheInfo.places.length +
          (isLed ? '，LED*' + ledNum + '=' + ledPrice : '') +
          (isTea ? '，茶歇*' + teaNum + '=' + teaPrice : '');

        newSchemeList.value.push({
          demandTd1: '会场',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 用餐
      if (props.schemeCacheInfo.caterings && props.schemeCacheInfo.caterings.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let cateringsPersonNum = 0;

        props.schemeCacheInfo.caterings.forEach((e) => {
          if (!e.schemeUnitPrice) return;

          cateringsPersonNum += e.schemePersonNum;

          demandTd3 += e.schemeUnitPrice * e.schemePersonNum;
        });

        demandTd2 = cateringsPersonNum ? '用餐人数*' + cateringsPersonNum : '-';

        newSchemeList.value.push({
          demandTd1: '用餐',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 用车
      if (props.schemeCacheInfo.vehicles && props.schemeCacheInfo.vehicles.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let brandList = [];
        let brandNumList = [];

        props.schemeCacheInfo.vehicles.forEach((e) => {
          if (!e.schemeUnitPrice) return;

          if (!brandList.includes(e.brand)) {
            brandList.push(e.brand);
          }

          demandTd3 += e.schemeUnitPrice * e.schemeVehicleNum;
        });

        brandList.forEach((e, idx) => {
          const sameCar = props.schemeCacheInfo.vehicles.filter((j) => j.brand === e);
          brandNumList[idx] = 0;

          sameCar.forEach((k) => {
            brandNumList[idx] += k.schemeVehicleNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + e + '*' + brandNumList[idx] + '辆';
        });

        newSchemeList.value.push({
          demandTd1: '用车',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 服务人员
      if (props.schemeCacheInfo.attendants && props.schemeCacheInfo.attendants.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let typeList = [];
        let typeNumList = [];

        props.schemeCacheInfo.attendants.forEach((e) => {
          if (!e.schemeUnitPrice) return;

          if (!typeList.includes(e.type)) {
            typeList.push(e.type);
          }

          demandTd3 += e.schemeUnitPrice * e.schemePersonNum;
        });

        typeList.forEach((e, idx) => {
          const sameType = props.schemeCacheInfo.attendants.filter((j) => j.type === e);
          typeNumList[idx] = 0;

          sameType.forEach((k) => {
            typeNumList[idx] += k.schemePersonNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + AttendantTypeConstant.ofType(e)?.desc + '*' + typeNumList[idx] + '人';
        });

        newSchemeList.value.push({
          demandTd1: '服务人员',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 方案拓展
      if (props.schemeCacheInfo.activities && props.schemeCacheInfo.activities.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let activitiesPersonNum = 0;

        props.schemeCacheInfo.activities.forEach((e) => {
          if (!e.schemeUnitPrice) return;

          activitiesPersonNum += e.schemePersonNum;

          demandTd3 += e.schemePersonNum * e.schemeUnitPrice;
        });

        demandTd2 = activitiesPersonNum ? '拓展*' + activitiesPersonNum + '人' : '-';

        newSchemeList.value.push({
          demandTd1: '方案拓展',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 保险
      if (props.schemeCacheInfo.insurances && props.schemeCacheInfo.insurances.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        let insList = [];
        let insNumList = [];

        props.schemeCacheInfo.insurances.forEach((e) => {
          if (!insList.includes(e.insuranceName)) {
            insList.push(e.insuranceName);
          }

          demandTd3 += e.schemeUnitPrice * e.schemePersonNum;
        });

        insList.forEach((e, idx) => {
          const sameCar = props.schemeCacheInfo.insurances.filter((j) => j.insuranceName === e);
          insNumList[idx] = 0;

          sameCar.forEach((k) => {
            insNumList[idx] += k.schemePersonNum;
          });

          demandTd2 += (idx > 0 ? '，' : '') + e + '*' + insNumList[idx] + '人';
        });

        newSchemeList.value.push({
          demandTd1: '保险',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 布展物料
      if (props.schemeCacheInfo.material && Object.keys(props.schemeCacheInfo.material).length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.material.materialDetails.forEach((e, idx) => {
          demandTd2 += (idx > 0 ? '，' : '') + MaterialTypeConstant.ofType(e.type)?.desc + '*' + e.schemeMaterialNum;

          demandTd3 += e.schemeUnitPrice * e.schemeMaterialNum;
        });

        newSchemeList.value.push({
          demandTd1: '布展物料',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 礼品
      if (props.schemeCacheInfo.presents && props.schemeCacheInfo.presents.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.presents.forEach((e, idx) => {
          demandTd2 +=
            (idx > 0 ? '，' : '') +
            (e.presentDetails[0]?.productName ? e.presentDetails[0]?.productName + '*' : '') +
            (e.presentDetails[0]?.schemePersonNum
              ? e.presentDetails[0]?.schemePersonNum + e.presentDetails[0]?.unit
              : '');

          demandTd3 += e.schemeTotalPrice;
        });

        newSchemeList.value.push({
          demandTd1: '礼品',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 其他
      if (props.schemeCacheInfo.others && props.schemeCacheInfo.others.length > 0) {
        let demandTd2 = '';
        let demandTd3 = 0;

        props.schemeCacheInfo.others.forEach((e, idx) => {
          demandTd2 += (idx > 0 ? '，' : '') + e.itemName + '*' + e.num + e.unit;

          demandTd3 += e.schemeTotalPrice;
        });

        newSchemeList.value.push({
          demandTd1: '其他',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }

      // 服务费
      if (
        props.schemeCacheInfo.serviceFee &&
        props.schemeCacheInfo.serviceFee.serviceFeeRate !== null &&
        props.schemeCacheInfo.serviceFee.schemeServiceFeeReal !== null
      ) {
        let demandTd2 = '收取比例' + props.schemeCacheInfo.serviceFee.serviceFeeRate + '%';
        let demandTd3 = props.schemeCacheInfo.serviceFee.schemeServiceFeeReal;

        newSchemeList.value.push({
          demandTd1: '服务费',
          demandTd2: demandTd2,
          demandTd3: demandTd3 ? formatNumberThousands(demandTd3) : '0.00',
        });
      }
    }
  },
);

const schemePlanLabelList = ['需求', '方案', '合计（元）'];

onMounted(async () => {});
</script>

<template>
  <!-- 方案合计 -->
  <div class="scheme_total">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>合计</span>
    </div>

    <div class="total_table mt16">
      <div class="scheme_total_list1">
        <div class="scheme_demand_label" v-for="(label, index) in schemePlanLabelList" :key="index">
          {{ label }}
          <div class="label_shu" v-show="index < 2"></div>
        </div>
      </div>
      <div class="scheme_total_list1 scheme_total_list2" v-for="(item, idx) in newSchemeList" :key="idx">
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ item.demandTd1 || '-' }}
            </template>
            {{ item.demandTd1 || '-' }}
          </a-tooltip>
        </div>
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ item.demandTd2 || '-' }}
            </template>
            {{ item.demandTd2 || '-' }}
          </a-tooltip>
        </div>
        <div class="scheme_demand_label">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ '¥' + item.demandTd3 || '-' }}
            </template>
            {{ '¥' + item.demandTd3 || '-' }}
          </a-tooltip>
        </div>
      </div>
    </div>

    <div class="scheme_total_btn mt16">
      <span class="total_text">合计：</span>
      <span class="total_num">{{
        '¥' +
        formatNumberThousands(
          props.totalPrice +
            (props.schemeCacheInfo?.serviceFee?.schemeServiceFeeReal
              ? props.schemeCacheInfo?.serviceFee?.schemeServiceFeeReal
              : 0),
        )
      }}</span>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_total {
  .total_table {
    .scheme_total_list1,
    .scheme_total_list2 {
      width: 50%;
      display: flex;

      height: 36px;
      line-height: 36px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #1d2129;
      border-bottom: 1px solid #e5e6eb;
      background: #f7f8fa;

      .scheme_demand_label {
        padding: 0 16px;
        width: 100px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        position: relative;

        &:nth-child(2) {
          width: calc(100% - 160px - 100px);
        }
        &:nth-child(3) {
          width: 160px;
          text-align: right;
        }

        .label_shu {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);

          width: 1px;
          height: 16px;
          background: #e5e6eb;
        }
      }
    }

    .scheme_total_list2 {
      background: #ffffff;
      font-weight: 400;
      color: #1d2129;
    }
  }

  .scheme_total_btn {
    width: 50%;

    padding: 0 14px;
    height: 44px;
    line-height: 44px;

    text-align: right;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    color: #f7f8fc;
    background: #1868db;
    border-radius: 4px;

    .total_text {
      font-size: 14px;
    }
    .total_num {
      font-size: 20px;
    }
  }
}
</style>
