<script setup lang="ts">
// 方案变更-酒店方案
import { onMounted, ref, watch, defineProps } from 'vue';

import { hotelLevelAllConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeHotels: {
    type: Array,
    default: [],
  },
});

const hotelLoading = ref<boolean>(false);

// 酒店方案
const hotelColumns = [
  {
    name: 'schemeIndex',
    dataIndex: '1',
    width: 72,
    ellipsis: true,
    align: 'center',
    className: 'interact_table_bgc_gray',
    customRender: ({ index }) => {
      return '酒店' + (index + 1);
    },
  },
  {
    title: 'schemeTitle',
    dataIndex: '2',
    // width: 280,
    ellipsis: true,
    customRender: ({ record }) => {
      return record.hotelName;
    },
  },
  {
    title: 'schemeValue',
    dataIndex: '3',
    // width: 240,
    ellipsis: true,
    customRender: ({ record }) => {
      return hotelLevelAllConstant.ofType(record.level)?.desc || '-';
    },
  },
];

onMounted(async () => {});
</script>

<template>
  <!-- 酒店方案 -->
  <div class="scheme_hotel">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>酒店方案</span>
    </div>

    <div class="common_table mt16">
      <div class="common_table_l">
        <a-table
          :columns="hotelColumns"
          :data-source="schemeHotels"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>
      </div>
      <div class="common_table_divide"></div>
      <div class="common_table_r flex">
        <a-table
          :loading="hotelLoading"
          :columns="hotelColumns"
          :data-source="schemeHotels"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_hotel {
}
</style>
